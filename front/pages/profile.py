"""
Profile# Add projec# Import backend modules for CVD calculation
try:
    from backend.modules.cvd_score import calculate_cvd_risk
except ImportError:
    calculate_cvd_risk = None

# Import UI componentsys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config import USERS_DB_PATH

# Import backend modules for CVD calculation
try:
    from backend.modules.cvd_score import calculate_cvd_risk
except ImportError:
    calculate_cvd_risk = None for MIT CVD App
Allows users to edit their profile information, family history, and lifestyle data
"""
import streamlit as st
import json
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config import USERS_DB_PATH

# Import backend modules for CVD calculation
try:
    from backend.modules.cvd_score import calculate_cvd_risk
except ImportError:
    calculate_cvd_risk = None

# Import UI components
try:
    from front.components.ui_components import (
        show_section_header, 
        show_update_success, 
        show_update_error,
        show_family_history_summary,
        show_lifestyle_summary,
        show_medical_conditions_summary
    )
except ImportError:
    # Fallback if components are not available
    def show_section_header(title, icon="📋", help_text=None):
        if help_text:
            st.subheader(f"{icon} {title}", help=help_text)
        else:
            st.subheader(f"{icon} {title}")
    
    def show_update_success(message="Information updated successfully!"):
        st.success(f"✅ {message}")
    
    def show_update_error(message="Failed to update information"):
        st.error(f"❌ {message}")
    
    def show_family_history_summary(family_history):
        pass
    
    def show_lifestyle_summary(lifestyle):
        pass
    
    def show_medical_conditions_summary(conditions, medications):
        pass

def load_users() -> list:
    """Load users from JSON file"""
    if os.path.exists(USERS_DB_PATH):
        try:
            with open(USERS_DB_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return []
    return []

def save_users(users: list) -> bool:
    """Save users to JSON file"""
    try:
        with open(USERS_DB_PATH, 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
        return True
    except (IOError, OSError):
        return False

def validate_profile_data(data: Dict[str, Any]) -> tuple[bool, str]:
    """Validate profile data before saving"""
    # Basic validation
    if 'name' in data and (not data['name'] or len(data['name'].strip()) < 2):
        return False, "Name must be at least 2 characters long"
    
    if 'age' in data and (data['age'] < 1 or data['age'] > 120):
        return False, "Age must be between 1 and 120"
    
    if 'email' in data and data['email'] and '@' not in data['email']:
        return False, "Please enter a valid email address"
    
    return True, "Valid"

def update_user_profile(user_id: str, updated_data: Dict[str, Any]) -> bool:
    """Update user profile data"""
    users = load_users()
    
    for i, user in enumerate(users):
        if user.get('id') == user_id:
            # Update the user data
            users[i].update(updated_data)
            users[i]['updated_at'] = datetime.now().isoformat()
            
            # Save back to file
            if save_users(users):
                # Update session state
                st.session_state.user = users[i]
                return True
    return False

def recalculate_cvd_score(user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Recalculate CVD score with updated profile data"""
    if calculate_cvd_risk is None:
        st.error("CVD risk calculation module not available")
        return None
    
    try:
        result = calculate_cvd_risk(user_data)
        if result.get('success', False):
            # Add the new score to user's CVD scores
            current_scores = user_data.get('cvd_scores', [])
            current_scores.append(result['score_data'])
            
            # Update user with new CVD score
            updated_data = {'cvd_scores': current_scores}
            if update_user_profile(user_data['id'], updated_data):
                # Make sure session state reflects the update
                st.session_state.user = load_users()[0] if load_users() else st.session_state.user
                for user in load_users():
                    if user.get('id') == user_data['id']:
                        st.session_state.user = user
                        break
                return result
            else:
                st.error("Failed to save CVD score to profile")
                return None
        else:
            # Return the result even if unsuccessful to show the error
            return result
    except Exception as e:
        st.error(f"Error calculating CVD score: {str(e)}")
    
    return None

def show_basic_info_section():
    """Show and edit basic user information"""
    show_section_header("Basic Information", "👤", "Update your personal information")
    
    user = st.session_state.user
    
    col1, col2 = st.columns(2)
    
    with col1:
        new_name = st.text_input("Full Name", value=user.get('name', ''))
        new_age = st.number_input("Age", min_value=1, max_value=120, value=user.get('age', 30))
    
    with col2:
        new_email = st.text_input("Email", value=user.get('email', ''))
        
        # Gender selection
        current_gender = user.get('gender', 'male')
        new_gender = st.selectbox(
            "Gender",
            options=["male", "female"],
            index=0 if current_gender == "male" else 1,
            format_func=lambda x: "Male" if x == "male" else "Female",
            help="Gender affects cardiovascular risk calculation"
        )
    
    if st.button("💾 Update Basic Information", key="update_basic"):
        updated_data = {
            'name': new_name,
            'age': new_age,
            'email': new_email,
            'gender': new_gender
        }
        
        # Validate data before saving
        is_valid, error_message = validate_profile_data(updated_data)
        if not is_valid:
            show_update_error(error_message)
            return
        
        if update_user_profile(user['id'], updated_data):
            show_update_success("Basic information updated successfully!")
            
            # Automatically recalculate CVD risk since age affects risk
            with st.spinner("Updating CVD risk assessment..."):
                result = recalculate_cvd_score(st.session_state.user)
                if result:
                    if result.get('success', False):
                        st.success(f"✅ CVD risk updated: {result['risk_level']} ({result['score']:.3f})")
                    else:
                        st.warning(f"⚠️ {result.get('error', 'Unable to calculate CVD risk - clinical data may be missing')}")
                else:
                    st.warning("⚠️ Unable to update CVD risk assessment")
            
            st.rerun()
        else:
            show_update_error("Failed to update basic information")

def show_family_history_section():
    """Show and edit family history"""
    show_section_header("Family Medical History", "🧬", "Track family medical history for accurate risk assessment")
    st.markdown("*Please indicate if any immediate family members (parents, siblings) have had these conditions:*")
    
    user = st.session_state.user
    family_history = user.get('profile', {}).get('family_history', {})
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Cardiovascular Conditions:**")
        heart_attack = st.checkbox(
            "💔 Heart Attack", 
            value=family_history.get('heart_attack', False),
            help="Family history of heart attack or myocardial infarction"
        )
        high_bp = st.checkbox(
            "🩸 High Blood Pressure (Hypertension)", 
            value=family_history.get('high_blood_pressure', False),
            help="Family history of hypertension"
        )
        stroke = st.checkbox(
            "🧠 Stroke", 
            value=family_history.get('stroke', False),
            help="Family history of stroke or cerebrovascular accident"
        )
    
    with col2:
        st.markdown("**Metabolic Conditions:**")
        diabetes = st.checkbox(
            "🍯 Diabetes", 
            value=family_history.get('diabetes', False),
            help="Family history of Type 1 or Type 2 diabetes"
        )
        high_cholesterol = st.checkbox(
            "🧈 High Cholesterol", 
            value=family_history.get('high_cholesterol', False),
            help="Family history of high cholesterol or lipid disorders"
        )
    
    # Advanced family history options
    with st.expander("🔍 Additional Family History Details"):
        col3, col4 = st.columns(2)
        
        with col3:
            st.markdown("**Early Onset (before age 55 for men, 65 for women):**")
            heart_attack_early = st.checkbox(
                "Early Heart Attack", 
                value=family_history.get('heart_attack_early_onset', False)
            )
            stroke_early = st.checkbox(
                "Early Stroke", 
                value=family_history.get('stroke_early_onset', False)
            )
            diabetes_early = st.checkbox(
                "Early Diabetes", 
                value=family_history.get('diabetes_early_onset', False)
            )
        
        with col4:
            st.markdown("**Multiple Family Members Affected:**")
            heart_attack_multiple = st.checkbox(
                "Multiple Heart Attacks", 
                value=family_history.get('heart_attack_multiple', False)
            )
            high_bp_multiple = st.checkbox(
                "Multiple High BP", 
                value=family_history.get('high_blood_pressure_multiple', False)
            )
            diabetes_multiple = st.checkbox(
                "Multiple Diabetes", 
                value=family_history.get('diabetes_multiple', False)
            )
    
    if st.button("💾 Update Family History", key="update_family"):
        updated_family_history = {
            'heart_attack': heart_attack,
            'high_blood_pressure': high_bp,
            'stroke': stroke,
            'diabetes': diabetes,
            'high_cholesterol': high_cholesterol,
            'heart_attack_early_onset': heart_attack_early,
            'stroke_early_onset': stroke_early,
            'diabetes_early_onset': diabetes_early,
            'heart_attack_multiple': heart_attack_multiple,
            'high_blood_pressure_multiple': high_bp_multiple,
            'diabetes_multiple': diabetes_multiple
        }
        
        # Get current profile
        current_profile = user.get('profile', {})
        current_profile['family_history'] = updated_family_history
        
        updated_data = {'profile': current_profile}
        
        if update_user_profile(user['id'], updated_data):
            show_update_success("Family history updated successfully!")
            
            # Automatically recalculate CVD risk since family history affects risk
            with st.spinner("Updating CVD risk assessment..."):
                result = recalculate_cvd_score(st.session_state.user)
                if result:
                    if result.get('success', False):
                        st.success(f"✅ CVD risk updated: {result['risk_level']} ({result['score']:.3f})")
                    else:
                        st.warning(f"⚠️ {result.get('error', 'Unable to calculate CVD risk - clinical data may be missing')}")
                else:
                    st.warning("⚠️ Unable to update CVD risk assessment")
            
            st.rerun()
        else:
            show_update_error("Failed to update family history")

def show_lifestyle_section():
    """Show and edit lifestyle information"""
    show_section_header("Lifestyle Information", "🏃‍♂️", "Manage your lifestyle factors that affect cardiovascular health")
    
    user = st.session_state.user
    lifestyle = user.get('profile', {}).get('lifestyle', {})
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Physical Activity & Habits:**")
        smoking = st.checkbox(
            "🚬 Current Smoker", 
            value=lifestyle.get('smoking', False),
            help="Do you currently smoke cigarettes?"
        )
        
        exercise_freq = st.selectbox(
            "🏃‍♂️ Exercise Frequency (per week)",
            options=[0, 1, 2, 3, 4, 5, 6, 7],
            index=lifestyle.get('exercise_frequency', 0),
            help="How many times per week do you engage in physical exercise?"
        )
        
        sleep_hours = st.slider(
            "😴 Sleep Hours (per night)",
            min_value=4.0,
            max_value=12.0,
            value=float(lifestyle.get('sleep_hours', 8)),
            step=0.5,
            help="Average hours of sleep per night"
        )
    
    with col2:
        st.markdown("**Mental Health & Social:**")
        stress_level = st.slider(
            "😰 Stress Level",
            min_value=1,
            max_value=10,
            value=lifestyle.get('stress_level', 5),
            help="Rate your average stress level (1=very low, 10=very high)"
        )
        
        social_support = st.slider(
            "👥 Social Support",
            min_value=1,
            max_value=10,
            value=lifestyle.get('social_support', 5),
            help="Rate your social support system (1=very low, 10=very high)"
        )
        
        work_life_balance = st.slider(
            "⚖️ Work-Life Balance",
            min_value=1,
            max_value=10,
            value=lifestyle.get('work_life_balance', 5),
            help="Rate your work-life balance (1=very poor, 10=excellent)"
        )
    
    # Additional lifestyle factors
    with st.expander("🔍 Additional Lifestyle Factors"):
        alcohol_consumption = st.selectbox(
            "🍺 Alcohol Consumption",
            options=["None", "Occasional", "Moderate", "Heavy"],
            index=["None", "Occasional", "Moderate", "Heavy"].index(
                lifestyle.get('alcohol_consumption', 'None')
            ),
            help="Frequency of alcohol consumption"
        )
        
        diet_type = st.selectbox(
            "🥗 Diet Type",
            options=["Standard", "Mediterranean", "Vegetarian", "Vegan", "Low-carb", "Other"],
            index=["Standard", "Mediterranean", "Vegetarian", "Vegan", "Low-carb", "Other"].index(
                lifestyle.get('diet_type', 'Standard')
            ),
            help="Primary diet type you follow"
        )
    
    if st.button("💾 Update Lifestyle Information", key="update_lifestyle"):
        updated_lifestyle = {
            'smoking': smoking,
            'exercise_frequency': exercise_freq,
            'sleep_hours': sleep_hours,
            'stress_level': stress_level,
            'social_support': social_support,
            'work_life_balance': work_life_balance,
            'alcohol_consumption': alcohol_consumption,
            'diet_type': diet_type
        }
        
        # Get current profile
        current_profile = user.get('profile', {})
        current_profile['lifestyle'] = updated_lifestyle
        
        updated_data = {'profile': current_profile}
        
        if update_user_profile(user['id'], updated_data):
            show_update_success("Lifestyle information updated successfully!")
            
            # Automatically recalculate CVD risk since lifestyle affects risk
            with st.spinner("Updating CVD risk assessment..."):
                result = recalculate_cvd_score(st.session_state.user)
                if result:
                    if result.get('success', False):
                        st.success(f"✅ CVD risk updated: {result['risk_level']} ({result['score']:.3f})")
                    else:
                        st.warning(f"⚠️ {result.get('error', 'Unable to calculate CVD risk - clinical data may be missing')}")
                else:
                    st.warning("⚠️ Unable to update CVD risk assessment")
            
            st.rerun()
        else:
            show_update_error("Failed to update lifestyle information")

def show_clinical_data_section():
    """Show and edit clinical data required for CVD risk calculation"""
    show_section_header("Clinical Data", "🩺", "Enter clinical measurements for accurate CVD risk assessment")
    
    user = st.session_state.user
    profile = user.get('profile', {})
    clinical = profile.get('clinical', {})
    
    st.info("💡 These measurements are required for accurate Framingham CVD risk calculation")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Cholesterol Levels (mg/dL)**")
        total_cholesterol = st.number_input(
            "Total Cholesterol",
            min_value=100.0,
            max_value=500.0,
            value=float(clinical.get('total_cholesterol', 200.0)),
            step=1.0,
            help="Normal: <200 mg/dL, Borderline: 200-239 mg/dL, High: ≥240 mg/dL"
        )
        
        hdl_cholesterol = st.number_input(
            "HDL Cholesterol (Good cholesterol)",
            min_value=20.0,
            max_value=100.0,
            value=float(clinical.get('hdl_cholesterol', 50.0)),
            step=1.0,
            help="Low: <40 mg/dL (men), <50 mg/dL (women), High: ≥60 mg/dL"
        )
    
    with col2:
        st.markdown("**Blood Pressure**")
        systolic_bp = st.number_input(
            "Systolic Blood Pressure (mmHg)",
            min_value=80.0,
            max_value=200.0,
            value=float(clinical.get('systolic_blood_pressure', 120.0)),
            step=1.0,
            help="Normal: <120 mmHg, Elevated: 120-129 mmHg, High: ≥130 mmHg"
        )
        
        hypertension_treatment = st.checkbox(
            "Currently taking blood pressure medication",
            value=clinical.get('hypertension_treatment', False),
            help="Check if you are currently taking medication to control blood pressure"
        )
        
        diabetes = st.checkbox(
            "Diagnosed with diabetes",
            value=clinical.get('diabetes', False),
            help="Check if you have been diagnosed with Type 1 or Type 2 diabetes"
        )
    
    if st.button("💾 Update Clinical Data", key="update_clinical"):
        # Get current profile
        current_profile = user.get('profile', {})
        current_profile['clinical'] = {
            'total_cholesterol': total_cholesterol,
            'hdl_cholesterol': hdl_cholesterol,
            'systolic_blood_pressure': systolic_bp,
            'hypertension_treatment': hypertension_treatment,
            'diabetes': diabetes
        }
        
        updated_data = {'profile': current_profile}
        
        if update_user_profile(user['id'], updated_data):
            show_update_success("Clinical data updated successfully!")
            
            # Automatically recalculate CVD risk since clinical data is essential for calculation
            with st.spinner("Updating CVD risk assessment..."):
                result = recalculate_cvd_score(st.session_state.user)
                if result:
                    if result.get('success', False):
                        st.success(f"✅ CVD risk calculated: {result['risk_level']} ({result['score']:.3f})")
                    else:
                        st.warning(f"⚠️ {result.get('error', 'Unable to calculate CVD risk')}")
            
            st.rerun()
        else:
            show_update_error("Failed to update clinical data")

def show_medical_conditions_section():
    """Show and edit medical conditions"""
    show_section_header("Medical Conditions & Medications", "🏥", "Track current medical conditions and medications")
    
    user = st.session_state.user
    profile = user.get('profile', {})
    medical_conditions = profile.get('medical_conditions', [])
    medications = profile.get('medications', [])
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Current Medical Conditions:**")
        condition_options = [
            "None", "Hypertension", "Diabetes Type 1", "Diabetes Type 2", 
            "High Cholesterol", "Heart Disease", "Asthma", "Arthritis", 
            "Depression", "Anxiety", "Drugs", "Other"
        ]
        
        selected_conditions = st.multiselect(
            "Select current conditions",
            options=condition_options,
            default=medical_conditions if medical_conditions else ["None"],
            help="Select all medical conditions that apply"
        )
        
        if "Other" in selected_conditions:
            other_condition = st.text_input("Specify other condition:")
            if other_condition and other_condition not in selected_conditions:
                selected_conditions.append(other_condition)
    
    with col2:
        st.markdown("**Current Medications:**")
        medication_text = st.text_area(
            "List medications (one per line)",
            value="\n".join(medications) if medications else "",
            height=100,
            help="List all current medications, supplements, or treatments"
        )
        
        medication_list = [med.strip() for med in medication_text.split('\n') if med.strip()]
    
    if st.button("💾 Update Medical Information", key="update_medical"):
        # Filter out "None" if other conditions are selected
        if len(selected_conditions) > 1 and "None" in selected_conditions:
            selected_conditions.remove("None")
        
        # Get current profile
        current_profile = user.get('profile', {})
        current_profile['medical_conditions'] = selected_conditions
        current_profile['medications'] = medication_list
        
        updated_data = {'profile': current_profile}
        
        if update_user_profile(user['id'], updated_data):
            show_update_success("Medical information updated successfully!")
            
            # Automatically recalculate CVD risk since medical conditions may affect risk
            with st.spinner("Updating CVD risk assessment..."):
                result = recalculate_cvd_score(st.session_state.user)
                if result:
                    if result.get('success', False):
                        st.success(f"✅ CVD risk updated: {result['risk_level']} ({result['score']:.3f})")
                    else:
                        st.warning(f"⚠️ {result.get('error', 'Unable to calculate CVD risk - clinical data may be missing')}")
                else:
                    st.warning("⚠️ Unable to update CVD risk assessment")
            
            st.rerun()
        else:
            show_update_error("Failed to update medical information")

def show_profile_summary():
    """Show a summary of the current profile"""
    show_section_header("Profile Summary", "📊", "Overview of your current health profile")
    
    user = st.session_state.user
    profile = user.get('profile', {})
    
    # Create columns for different summary sections
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("**👤 Basic Info**")
        st.write(f"**Name:** {user.get('name', 'N/A')}")
        st.write(f"**Age:** {user.get('age', 'N/A')}")
        st.write(f"**Email:** {user.get('email', 'N/A')}")
        
        # Display gender
        gender = user.get('gender', 'N/A')
        if gender != 'N/A':
            gender_display = "Male" if gender == 'male' else "Female"
            st.write(f"**Gender:** {gender_display}")
        else:
            st.write(f"**Gender:** {gender}")
    
    with col2:
        st.markdown("**🏃‍♂️ Lifestyle**")
        lifestyle = profile.get('lifestyle', {})
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq >= 3:
            st.success(f"Active ({exercise_freq}x/week)")
        elif exercise_freq >= 1:
            st.warning(f"Light ({exercise_freq}x/week)")
        else:
            st.error("Sedentary")
        
        smoking = lifestyle.get('smoking', False)
        if smoking:
            st.error("🚬 Smoker")
        else:
            st.success("🚭 Non-smoker")
    
    with col3:
        st.markdown("**🧬 Family History**")
        family_history = profile.get('family_history', {})
        conditions = []
        for condition, present in family_history.items():
            if present and not condition.endswith('_early_onset') and not condition.endswith('_multiple'):
                conditions.append(condition.replace('_', ' ').title())
        
        if conditions:
            if len(conditions) <= 2:
                for condition in conditions:
                    st.write(f"• {condition}")
            else:
                st.write(f"• {conditions[0]}")
                st.write(f"• +{len(conditions)-1} more")
        else:
            st.success("No history recorded")
    
    with col4:
        st.markdown("**📈 Comprehensive CVD Risk**")
        cvd_scores = user.get('cvd_scores', [])
        if cvd_scores:
            latest_score = cvd_scores[-1]
            score_value = latest_score['score']
            risk_level = latest_score['risk_level']

            # Display risk level with appropriate styling
            if risk_level == "Low":
                st.success(f"🟢 {risk_level}")
            elif risk_level == "Borderline":
                st.warning(f"🟡 {risk_level}")
            elif risk_level == "Intermediate":
                st.error(f"🟠 {risk_level}")
            elif risk_level == "High":
                st.error(f"🔴 {risk_level}")
            else:
                st.info(f"❓ {risk_level}")

            # Show 10-year risk percentage
            if score_value is not None:
                st.metric("10-Year Risk", f"{score_value:.1%}")
                
                # Show breakdown if available
                if hasattr(latest_score, 'get') and latest_score.get('factors'):
                    factors = latest_score['factors']

                    # Create expandable breakdown
                    with st.expander("📊 Risk Score Breakdown"):
                        # Check for new format first (Framingham-based)
                        if 'framingham_risk' in factors:
                            framingham_pct = factors['framingham_risk'] * 100
                            st.write(f"🔬 **Framingham Base**: {framingham_pct:.1f}%")

                            # Handle family history adjustment
                            family_adj_risk = factors.get('family_adjusted_risk', factors.get('adjusted_risk', factors['framingham_risk']))
                            family_adj = family_adj_risk * 100
                            family_impact = family_adj - framingham_pct
                            if abs(family_impact) > 0.01:  # Only show if there's a meaningful impact
                                st.write(f"🧬 **After Family History**: {family_adj:.1f}% ({family_impact:+.1f}%)")
                            else:
                                st.write(f"🧬 **After Family History**: {family_adj:.1f}% (no family history factors)")

                            # Handle lifestyle adjustment
                            lifestyle_adj_risk = factors.get('lifestyle_adjusted_risk', family_adj_risk)
                            lifestyle_adj = lifestyle_adj_risk * 100
                            lifestyle_impact = lifestyle_adj - family_adj
                            if abs(lifestyle_impact) > 0.01:  # Only show if there's a meaningful impact
                                st.write(f"🏃‍♂️ **After Lifestyle**: {lifestyle_adj:.1f}% ({lifestyle_impact:+.1f}%)")
                            else:
                                st.write(f"🏃‍♂️ **After Lifestyle**: {lifestyle_adj:.1f}% (no lifestyle adjustments)")

                        # Fallback to old format if new format not available
                        elif 'age_risk' in factors:
                            st.write("⚠️ **Legacy Format** - Recalculate for detailed breakdown")
                            if 'age_risk' in factors:
                                st.write(f"🎂 **Age Factor**: {factors.get('age_risk', 0):.1f}%")
                            if 'family_risk' in factors:
                                st.write(f"🧬 **Family Factor**: {factors.get('family_risk', 0):.1f}%")
                            if 'lifestyle_risk' in factors:
                                st.write(f"🏃‍♂️ **Lifestyle Factor**: {factors.get('lifestyle_risk', 0):.1f}%")

                        else:
                            st.write("ℹ️ Detailed breakdown not available - recalculate risk score")
            else:
                st.metric("Status", "Incomplete Data")

            # Show last update time
            last_update = latest_score.get('calculated_at', '')
            if last_update:
                st.caption(f"Updated: {last_update[:10]}")

            # Show methodology
            st.caption("Includes Framingham + Lifestyle + Family History")
        else:
            st.info("No assessment yet")
            st.caption("Complete clinical profile to calculate")

def main():
    """Main profile management interface"""
    st.title("👤 Profile Management - aidctive")
    
    # Check if user is logged in
    if 'user' not in st.session_state or not st.session_state.user:
        st.error("❌ You need to be logged in to access your profile.")
        st.info("🔑 Please log in first.")
        return
    
    # Profile summary at the top
    show_profile_summary()
    
    st.markdown("---")
    
    # Tab interface for different sections
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "👤 Basic Info", 
        "🧬 Family History", 
        "🏃‍♂️ Lifestyle", 
        "🩺 Clinical Data",
        "🏥 Medical Info"
    ])
    
    with tab1:
        show_basic_info_section()
    
    with tab2:
        show_family_history_section()
    
    with tab3:
        show_lifestyle_section()
    
    with tab4:
        show_clinical_data_section()
    
    with tab5:
        show_medical_conditions_section()
    
    # Action buttons at the bottom
    st.markdown("---")
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("� Generate Health Report", key="gen_report", use_container_width=True):
            st.info("📄 Health report generation feature coming soon!")
    
    with col2:
        if st.button("🏠 Back to Dashboard", key="back_home", use_container_width=True):
            st.session_state.current_page = "Home"
            st.rerun()

if __name__ == "__main__":
    main()
